<template>
  <div class="table-header-container">
    <table class="header-table">
      <thead class="table-header-sticky">
        <tr class="header-row">
          <th
            v-for="column in columns"
            :key="column.key"
            :class="getHeaderClass(column)"
            :style="getHeaderStyle(column)"
            @click="handleHeaderClick(column)"
            @mousedown="startResize($event, column)"
          >
            <!-- Selection Column -->
            <div v-if="column.key === 'select'" class="select-all-container">
              <input
                type="checkbox"
                class="select-all-checkbox"
                :checked="selectAll"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
                title="Select All"
              />
              <span class="select-all-label">All</span>
            </div>

            <!-- Regular Column -->
            <div v-else class="header-content">
              <span class="header-label">{{ column.label }}</span>
              
              <!-- Sort Indicator -->
              <div v-if="column.sortable" class="sort-indicator">
                <CIcon
                  v-if="getSortDirection(column.key)"
                  :name="getSortIcon(column.key)"
                  :class="getSortIconClass(column.key)"
                />
                <CIcon
                  v-else
                  name="cil-sort"
                  class="sort-icon-inactive"
                />
                
                <!-- Multi-sort priority -->
                <span
                  v-if="getSortPriority(column.key) && getSortPriority(column.key) > 1"
                  class="sort-priority"
                >
                  {{ getSortPriority(column.key) }}
                </span>
              </div>
            </div>

            <!-- Column Resizer -->
            <div
              v-if="column.resizable !== false"
              class="column-resizer"
              @mousedown.stop="startResize($event, column)"
            ></div>
          </th>
        </tr>
      </thead>
    </table>
  </div>
</template>

<script>
export default {
  name: 'TableHeader',
  
  props: {
    columns: {
      type: Array,
      required: true
    },
    sortColumn: {
      type: String,
      default: null
    },
    sortDirection: {
      type: String,
      default: 'asc'
    },
    sortIndicators: {
      type: Object,
      default: () => ({})
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selectAll: {
      type: Boolean,
      default: false
    },
    isIndeterminate: {
      type: Boolean,
      default: false
    },
    selectedCount: {
      type: Number,
      default: 0
    },
    totalCount: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      resizing: false,
      resizeColumn: null,
      resizeStartX: 0,
      resizeStartWidth: 0
    }
  },

  computed: {
    // Computed properties removed - using props instead
  },

  mounted() {
    // Add global mouse event listeners for column resizing
    document.addEventListener('mousemove', this.handleResize)
    document.addEventListener('mouseup', this.stopResize)
  },

  beforeDestroy() {
    // Remove global event listeners
    document.removeEventListener('mousemove', this.handleResize)
    document.removeEventListener('mouseup', this.stopResize)
  },

  methods: {
    /**
     * Get header cell CSS classes
     */
    getHeaderClass(column) {
      return [
        'header-cell',
        `header-cell--${column.key}`,
        {
          'header-cell--sortable': column.sortable,
          'header-cell--sorted': this.getSortDirection(column.key),
          'header-cell--select': column.key === 'select',
          'header-cell--fixed-left': column.fixed === 'left',
          'header-cell--fixed-right': column.fixed === 'right',
          'header-cell--resizable': column.resizable !== false
        }
      ]
    },

    /**
     * Get header cell inline styles
     */
    getHeaderStyle(column) {
      const styles = {}
      
      if (column.width) {
        if (typeof column.width === 'number') {
          styles.width = `${column.width}px`
          styles.minWidth = `${column.width}px`
        } else {
          styles.width = column.width
          styles.minWidth = column.width
        }
      }
      
      if (column.align) {
        styles.textAlign = column.align
      }
      
      return styles
    },

    /**
     * Handle header click for sorting
     */
    handleHeaderClick(column) {
      if (column.sortable && column.key !== 'select') {
        this.$emit('sort', column.key)
      }
    },

    /**
     * Handle select all checkbox
     */
    handleSelectAll(event) {
      this.$emit('select-all', event.target.checked)
    },

    /**
     * Get sort direction for column
     */
    getSortDirection(columnKey) {
      const indicator = this.sortIndicators[columnKey]
      return indicator ? indicator.direction : null
    },

    /**
     * Get sort priority for column
     */
    getSortPriority(columnKey) {
      const indicator = this.sortIndicators[columnKey]
      return indicator ? indicator.priority : null
    },

    /**
     * Get sort icon name
     */
    getSortIcon(columnKey) {
      const direction = this.getSortDirection(columnKey)
      return direction === 'asc' ? 'cil-sort-ascending' : 'cil-sort-descending'
    },

    /**
     * Get sort icon CSS class
     */
    getSortIconClass(columnKey) {
      const direction = this.getSortDirection(columnKey)
      return [
        'sort-icon',
        `sort-icon--${direction}`,
        {
          'sort-icon--active': !!direction
        }
      ]
    },

    /**
     * Start column resize
     */
    startResize(event, column) {
      if (column.resizable === false) return
      
      event.preventDefault()
      this.resizing = true
      this.resizeColumn = column
      this.resizeStartX = event.clientX
      
      // Get current column width
      const headerCell = event.target.closest('.header-cell')
      if (headerCell) {
        this.resizeStartWidth = headerCell.offsetWidth
      }
      
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    },

    /**
     * Handle column resize
     */
    handleResize(event) {
      if (!this.resizing || !this.resizeColumn) return
      
      event.preventDefault()
      const deltaX = event.clientX - this.resizeStartX
      const newWidth = Math.max(50, this.resizeStartWidth + deltaX) // Minimum width of 50px
      
      this.$emit('column-resize', this.resizeColumn, newWidth)
    },

    /**
     * Stop column resize
     */
    stopResize() {
      if (this.resizing) {
        this.resizing = false
        this.resizeColumn = null
        this.resizeStartX = 0
        this.resizeStartWidth = 0
        
        document.body.style.cursor = ''
        document.body.style.userSelect = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* Table Header Styles - Matching GenericDataTable Design */

.table-header-container {
  position: sticky;
  top: 0;
  z-index: 200;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 2px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.table-header-sticky {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: sticky;
  top: 0;
  z-index: 200;
}

.header-row {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 2px solid #e2e8f0;
}

.header-cell {
  position: relative;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: 600;
  font-size: 14px;
  color: white;
  text-align: left;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: none;
  transition: all 0.2s ease;

  &:last-child {
    border-right: none;
  }

  &--sortable {
    cursor: pointer;

    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);

      .sort-icon-inactive {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  &--sorted {
    background: linear-gradient(135deg, #4c51bf 0%, #5a67d8 100%);
    color: white;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &--select {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    text-align: center;
    padding: 16px 12px;
  }

  &--fixed-left {
    position: sticky;
    left: 0;
    z-index: 201;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }

  &--fixed-right {
    position: sticky;
    right: 0;
    z-index: 201;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  }

  &--resizable {
    &:hover .column-resizer {
      opacity: 1;
    }
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

.header-label {
  flex: 1;
  font-weight: 600;
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sort-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.sort-icon {
  width: 16px;
  height: 16px;
  transition: all 0.2s ease;

  &--active {
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  }

  &--asc {
    color: #68d391;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  }

  &--desc {
    color: #fbb6ce;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  }
}

.sort-icon-inactive {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.2s ease;
}

.sort-priority {
  background: #1e40af;
  color: white;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 4px;
  border-radius: 3px;
  min-width: 16px;
  text-align: center;
}

.select-all-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.select-all-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #10b981;
  transform: scale(1.1);

  &:checked {
    accent-color: #10b981;
    background-color: #10b981;
  }

  &:indeterminate {
    accent-color: #f59e0b;
    background-color: #f59e0b;
  }

  /* Enhanced visibility against header background */
  &:checked,
  &:indeterminate {
    border: 2px solid white;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  }
}

.select-all-label {
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: #6366f1;
  cursor: col-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-cell {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .header-label {
    font-size: 13px;
  }
  
  .sort-icon,
  .sort-icon-inactive {
    width: 14px;
    height: 14px;
  }
  
  .select-all-checkbox {
    width: 16px;
    height: 16px;
  }
  
  .select-all-label {
    font-size: 10px;
  }
}

@media (max-width: 576px) {
  .header-cell {
    padding: 10px 12px;
    font-size: 12px;
  }
  
  .header-content {
    gap: 4px;
  }
  
  .sort-priority {
    font-size: 9px;
    padding: 1px 3px;
  }
}

/* Dark Theme */
.vue2-datatable--dark {
  .table-header-container,
  .header-table,
  .table-header-sticky,
  .header-row,
  .header-cell {
    background: #1a202c;
    color: white;
    border-color: #2d3748;
  }
  
  .header-cell {
    &--sortable:hover {
      background: #2d3748;
    }
    
    &--sorted {
      background: #2a4365;
      color: #90cdf4;
    }
  }
  
  .header-label {
    color: white;
  }
  
  .select-all-label {
    color: #a0aec0;
  }
}

/* Print Styles */
@media print {
  .table-header-container {
    position: static;
  }
  
  .column-resizer {
    display: none;
  }
  
  .sort-indicator {
    display: none;
  }
}
</style>
