import DashboardRoutes from './routes/Dashboard.routes'
import ChangePasswordRoutes from './routes/ChangePassword.routes'
import ChangeProfilePictureRoutes from './routes/ChangeProfilePicture.routes'
import Profile from './routes/Profile.routes'
import SupportRoutes from './routes/Support.routes'
import ThemeRoutes from '../theme'
import SettingsRoutes from '../settings'
import VisitsRoutes from '../visits'
import RequestsRoutes from '../requests'
import ToolsRoutes from '../tools'
import ReportRoutes from '../reports'
import WidgetsRoutes from './routes/Widgets.routes'
import WidgetSettingsViewRoutes from './routes/WidgetSettingsView.routes'
import LockScreenRoutes from './routes/LockScreen.routes'
import CalendarRoutes from './routes/Calendar.routes'
import HelpRoutes from './routes/Help.routes'
import CommunicationRoutes from '../communications/index'
import coachingRoutes from '../coaching/index'
import quizRoutes from '../quiz/index'

// Test routes
const TestRoutes = [
    {
        path: '/test/vue2-datatable',
        name: 'Vue2DataTableTest',
        component: () => import('../../views/test/Vue2DataTableTest.vue'),
        meta: {
            title: 'Vue2 DataTable Test',
            requiresAuth: false
        }
    },
    {
        path: '/test/virtual-scroll-reactivity',
        name: 'VirtualScrollReactivityTest',
        component: () => import('../../views/test/VirtualScrollReactivityTest.vue'),
        meta: {
            title: 'Virtual Scroll Reactivity Test',
            requiresAuth: false
        }
    }
]

export default [
    ...LockScreenRoutes,
    ...DashboardRoutes,
    ...ChangePasswordRoutes,
    ...ChangeProfilePictureRoutes,
    ...SupportRoutes,
    ...ThemeRoutes,
    ...SettingsRoutes,
    ...VisitsRoutes,
    ...RequestsRoutes,
    ...ToolsRoutes,
    ...ReportRoutes,
    // ...DashboardChartsRoutes,
    // ...ChartLabelsRoutes,
    ...WidgetsRoutes,
    ...WidgetSettingsViewRoutes,
    ...CalendarRoutes,
    ...HelpRoutes,
    ...CommunicationRoutes,
    ...Profile,
    ...coachingRoutes,
    ...quizRoutes,
    ...TestRoutes,
]
